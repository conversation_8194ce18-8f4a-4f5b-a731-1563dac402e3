import * as React from 'react';

import { LabelAndValue } from '@/@types/LabelAndValue';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Input } from './input';

type ComboboxProps = {
  label: string;
  placeholder: string;
  options: LabelAndValue[];
  value: LabelAndValue;
  onChange: (value: LabelAndValue) => void;
  errorMessage?: string;
};

export function Combobox({
  label,
  placeholder,
  options,
  value,
  onChange,
  errorMessage,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover
      open={open}
      onOpenChange={setOpen}
      modal
    >
      <PopoverTrigger asChild>
        <Input
          label={label}
          placeholder={placeholder}
          value={value?.label ?? null}
          className="justify-start text-start cursor-pointer"
          errorMessage={errorMessage}
        />
      </PopoverTrigger>

      <PopoverContent
        className="p-0 w-[415px]"
        onWheel={(e) => {
          e.stopPropagation();
        }}
      >
        <Command>
          <CommandInput placeholder="Pesquisar..." />
          <CommandList>
            <CommandEmpty>Não encontrado.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.label}
                  onSelect={() => {
                    onChange(option);
                    setOpen(false);
                  }}
                >
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
