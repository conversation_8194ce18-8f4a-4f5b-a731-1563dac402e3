import { useLocation } from 'react-router-dom';
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '../ui/breadcrumb';

export function BreadcrumbMenu() {
  const location = useLocation();

  const pathnames = location.pathname.split('/').filter(Boolean);

  return (
    <div className="w-full h-16 bg-[#27B5BF1F] px-6 flex items-center shadow-sm">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/app/home">Início</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/app/${pathnames[1]}`}>
              {pathnames[1].charAt(0).toUpperCase() + pathnames[1].slice(1)}
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
