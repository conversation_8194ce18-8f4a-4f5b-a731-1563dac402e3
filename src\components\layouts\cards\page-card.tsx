import Icons from '@/components/Icons';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

type PageCardProps = {
  title: string;
  icon: React.ReactNode;
  link: string;
  targetBlank?: boolean;
};

export function PageCard({ title, icon, link, targetBlank }: PageCardProps) {
  return (
    <div className="w-[256px] h-[110px] bg-white rounded-lg shadow-lg p-4 flex flex-col justify-between">
      <div className="text-primary">
        {icon ?? <Icons.RABs.BsFolder size={20} />}
      </div>

      <div className="flex justify-between items-center w-full">
        <Tooltip>
          <TooltipTrigger className="text-start">
            <span className="text-md text-neutral-800 font-medium line-clamp-1">
              {title}
            </span>
          </TooltipTrigger>
          <TooltipContent>{title}</TooltipContent>
        </Tooltip>

        <Link
          to={link}
          target={targetBlank ? '_blank' : '_self'}
        >
          <Button
            variant="default"
            size="sm"
            className="rounded-md"
          >
            <ArrowRight />
          </Button>
        </Link>
      </div>
    </div>
  );
}
