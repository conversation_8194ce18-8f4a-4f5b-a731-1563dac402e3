import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { useQuery } from '@tanstack/react-query';
import { moduleOptions } from './data';
import { Link } from 'react-router-dom';

export function SidebarComp() {
  const { user } = useAuth();

  const {
    menus: { loadMenus },
  } = useApi();

  const { data: allMenus } = useQuery({
    queryKey: ['menus', user],
    queryFn: () =>
      loadMenus({
        ativo: true,
        empresa: user.companyId as string,
        perfil: user?.currentRole.id,
      }),
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  return (
    <Sidebar
      className="sticky w-[256px] h-[calc(100vh-80px)]"
      variant="sidebar"
    >
      <SidebarContent className="bg-white p-2">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  className="h-10 p-4 rounded-lg"
                  isActive={window.location.pathname === moduleOptions[0].url}
                >
                  <Link to={moduleOptions[0].url}>
                    {moduleOptions[0].icon}
                    <span className="text-sm">{moduleOptions[0].name}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              {allMenus?.result?.map((menu) => {
                const moduleExist = moduleOptions.find(
                  (moduleOption) => moduleOption.value === menu.module,
                );
                if (moduleExist) {
                  return (
                    <SidebarMenuItem key={moduleExist.name}>
                      <SidebarMenuButton
                        asChild
                        className="h-10 p-4 rounded-lg"
                        isActive={window.location.pathname === moduleExist.url}
                      >
                        <Link to={moduleExist.url}>
                          {moduleExist.icon}
                          <span className="text-sm">{moduleExist.name}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                }
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
